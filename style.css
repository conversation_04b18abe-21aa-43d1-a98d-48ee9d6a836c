/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
    padding: 20px 0;
    transition: all 0.3s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile Navigation - DISABLED */
.mobile-nav {
    display: none !important;
}

.nav-btn {
    display: none;
}

.nav-btn.active {
    display: none;
}

.nav-btn:hover:not(.active) {
    display: none;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 30px;
    background: white;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border-radius: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header styling */
header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 25px;
    border-bottom: 3px solid #4CAF50;
    position: relative;
}

header::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 2px;
}

h1 {
    color: #2c3e50;
    font-size: 2.8em;
    margin-bottom: 10px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h2 {
    color: #34495e;
    font-size: 1.6em;
    margin-bottom: 20px;
    padding-left: 15px;
    border-left: 5px solid #4CAF50;
    position: relative;
    font-weight: 600;
}

/* Form sections */
.form-section {
    background: #fafafa;
    padding: 25px;
    margin-bottom: 25px;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    position: relative;
    transition: all 0.3s ease;
    display: block; /* Always visible */
}

.form-section.active {
    display: block;
}

.form-section:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transform: translateY(-2px);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 250px;
    position: relative;
}

/* Labels */
label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Input styling with enhanced mobile support */
input, textarea, select {
    width: 100%;
    padding: 18px;
    font-size: 16px; /* Prevents zoom on iOS */
    border: 2px solid #ddd;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    touch-action: manipulation;
}

/* Location button styling */
.location-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 36px;
    min-width: 36px;
}

.location-btn:hover {
    background: #45a049;
}

.form-group {
    position: relative;
}

/* Inspector field - make it smaller */
#inspector {
    max-width: 200px;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    transform: translateY(-1px);
}

input:invalid {
    border-color: #e74c3c;
}

input:valid {
    border-color: #27ae60;
}

/* Special input types */
input[type="datetime-local"] {
    color-scheme: light;
}

select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 9L1.5 4.5h9z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 12px;
    appearance: none;
}

textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

/* Canvas styling */
.canvas-container {
    background: white;
    border: 2px solid #ddd;
    border-radius: 12px;
    padding: 15px;
    margin-top: 10px;
}

#drawing-canvas {
    border: 2px dashed #ccc;
    border-radius: 8px;
    background: white;
    display: block;
    margin: 0 auto;
    touch-action: none;
    cursor: crosshair;
    transition: border-color 0.3s ease;
}

#drawing-canvas:hover {
    border-color: #4CAF50;
}

.canvas-controls {
    margin-top: 15px;
    text-align: center;
    gap: 15px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.canvas-controls button {
    padding: 10px 20px;
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.canvas-controls button:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Image preview */
.image-preview {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    min-height: 60px;
}

.image-preview:empty::after {
    content: 'Images will appear here after upload';
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.image-preview img {
    max-width: 150px;
    max-height: 150px;
    border-radius: 8px;
    border: 2px solid #ddd;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.image-preview img:hover {
    transform: scale(1.05);
    border-color: #4CAF50;
}

/* File input styling */
input[type="file"] {
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    background: #f8f9fa;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="file"]:hover {
    border-color: #4CAF50;
    background: #e8f5e8;
}

/* Form actions */
.form-actions {
    text-align: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #e0e0e0;
    gap: 20px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button styling */
button {
    padding: 18px 35px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

button[type="submit"] {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

button[type="submit"]:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(76, 175, 80, 0.4);
}

button[type="submit"]:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#preview-btn {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

#preview-btn:hover {
    background: linear-gradient(135deg, #1976D2, #1565C0);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(33, 150, 243, 0.4);
}

/* Enhanced mobile responsive design */
@media (max-width: 768px) {
    body {
        padding: 70px 5px 10px 5px;
    }
    
    .container {
        margin: 5px;
        padding: 15px;
        border-radius: 15px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .form-group {
        min-width: 100%;
    }
    
    h1 {
        font-size: 2.2em;
    }
    
    h2 {
        font-size: 1.4em;
        margin-bottom: 15px;
    }
    
    .form-section {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    #drawing-canvas {
        width: 100%;
        height: 250px;
        max-width: 100%;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: center;
        gap: 15px;
        position: sticky;
        bottom: 10px;
        background: white;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    
    button {
        width: 100%;
        max-width: 300px;
        min-height: 50px;
        font-size: 16px;
        padding: 15px 25px;
    }
    
    .canvas-controls {
        flex-direction: row;
        gap: 10px;
        justify-content: space-around;
    }
    
    .canvas-controls button {
        flex: 1;
        max-width: none;
        min-height: 44px;
    }
    
    /* Improved touch targets */
    input, textarea, select {
        min-height: 50px;
        padding: 15px;
        font-size: 16px;
    }
    
    label {
        font-size: 16px;
        margin-bottom: 8px;
    }
    
    /* Better spacing for touch */
    .form-group {
        margin-bottom: 20px;
    }
}

/* Enhanced touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    input, textarea, select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 18px;
        min-height: 50px;
        border-radius: 12px;
    }
    
    button {
        padding: 18px 25px;
        font-size: 16px;
        min-height: 50px;
        border-radius: 12px;
        touch-action: manipulation;
    }
    
    .canvas-controls button {
        padding: 15px 20px;
        font-size: 16px;
        min-height: 48px;
    }
    
    #drawing-canvas {
        min-height: 300px;
        border-radius: 12px;
        touch-action: none;
    }
    
    /* Larger touch targets */
    .nav-btn {
        min-height: 48px;
        font-size: 14px;
    }
    
    .location-btn {
        min-height: 40px;
        min-width: 40px;
        padding: 10px;
    }
    
    /* Improved text readability */
    label {
        font-size: 16px;
        font-weight: 600;
    }
    
    h1 {
        font-size: 2em;
    }
    
    h2 {
        font-size: 1.5em;
    }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
    .container {
        max-width: 90%;
        padding: 25px;
    }
    
    .form-row {
        gap: 20px;
    }
    
    .form-group {
        min-width: 280px;
    }
    
    #drawing-canvas {
        width: 100%;
        max-width: 400px;
        height: 200px;
    }
    
    .form-actions {
        gap: 20px;
    }
}

/* Landscape phone optimizations */
@media (max-width: 768px) and (orientation: landscape) {
    body {
        padding-top: 60px;
    }
    
    .mobile-nav {
        padding: 6px;
    }
    
    .nav-btn {
        padding: 10px 6px;
        font-size: 11px;
        min-height: 40px;
    }
    
    .container {
        padding: 15px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    #drawing-canvas {
        height: 180px;
    }
}

/* Dark mode mobile enhancements */
body.dark-mode .mobile-nav {
    background: rgba(30, 41, 59, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode .nav-btn {
    color: #94a3b8;
}

body.dark-mode .nav-btn:hover:not(.active) {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

body.dark-mode .form-actions {
    background: #1e293b;
    box-shadow: 0 -5px 15px rgba(0,0,0,0.3);
}

/* Loading animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.loading {
    animation: pulse 1s infinite;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

/* Form section transitions */
.form-section {
    animation: fadeIn 0.3s ease-in-out;
}

/* Mobile nav animations */
.mobile-nav {
    animation: slideIn 0.3s ease-in-out;
}

.nav-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-btn:active {
    transform: scale(0.95);
}

/* Success/Error states */
.success {
    border-color: #27ae60 !important;
    background-color: #d5f4e6 !important;
}

.error {
    border-color: #e74c3c !important;
    background-color: #fdf2f2 !important;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus, input:focus, textarea:focus, select:focus {
    outline: 3px solid #4CAF50;
    outline-offset: 2px;
}

/* Dark mode styles */
body.dark-mode {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #e2e8f0;
}

body.dark-mode .container {
    background: #1e293b;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

body.dark-mode .form-section {
    background: #334155;
    border-color: #475569;
}

body.dark-mode h1 {
    color: #f1f5f9;
}

body.dark-mode h2 {
    color: #e2e8f0;
}

body.dark-mode label {
    color: #e2e8f0;
}

body.dark-mode input, 
body.dark-mode textarea, 
body.dark-mode select {
    background: #475569;
    border-color: #64748b;
    color: #e2e8f0;
}

body.dark-mode input:focus, 
body.dark-mode textarea:focus, 
body.dark-mode select:focus {
    border-color: #4CAF50;
    background: #475569;
}

body.dark-mode .canvas-container {
    background: #475569;
    border-color: #64748b;
}

body.dark-mode #drawing-canvas {
    background: #f8fafc;
}

body.dark-mode .image-preview {
    background: #475569;
}

body.dark-mode input[type="file"] {
    background: #475569;
    border-color: #64748b;
    color: #e2e8f0;
}

body.dark-mode input[type="file"]:hover {
    background: #64748b;
}

body.dark-mode .image-preview:empty::after {
    color: #94a3b8;
}

/* Dark mode toggle button */
.dark-mode-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.dark-mode-toggle:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

body.dark-mode .dark-mode-toggle {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

body.dark-mode .dark-mode-toggle:hover {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}